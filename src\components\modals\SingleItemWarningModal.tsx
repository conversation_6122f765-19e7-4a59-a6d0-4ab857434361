import { CloseCircle } from 'iconsax-react';
import React, { useEffect } from 'react';
import box from '../../assets/images/res-box.png';
interface SingleItemWarningModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddMore: () => void;
  onContinueAnyway: () => void;
}

export const SingleItemWarningModal: React.FC<SingleItemWarningModalProps> = ({
  isOpen,
  onClose,
  onAddMore,
  onContinueAnyway,
}) => {
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="absolute inset-0 bg-black/20 bg-opacity-50"
        onClick={onClose}
      />

      <div className="relative bg-white mx-3 md:mx-0 rounded-[20px] max-w-[782px] w-full shadow-2xl">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 transition-colors"
          aria-label="Close modal">
          <CloseCircle size="40" color="#634C42" variant="Bulk" />
        </button>
        <div className="flex flex-col lg:flex-row ">
          <div className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] rounded-t-[20px] lg:rounded-t-[0px]  lg:rounded-l-[20px] flex justify-center items-center lg:max-w-[256px] w-full">
            <img src={box} alt="gift-registry" />
          </div>

          <div className="py-8 lg:ml-8 px-2 lg:px-0 ">
            <div className="text-center lg:text-start">
              <h2 className="lg:text-[36px] text-2xl tracking-[-0.03em] font-medium mb-3 lg:max-w-[419px] w-full">
                You are about to add only
                <span className="text-primary italic"> 1 item</span> to your
                Gift Registry{' '}
              </h2>
              <p className="text-grey-250  lg:max-w-[450px] w-full text-base  lg:text-lg leading-relaxed mb-[42px]">
                We noticed you are about to add just an item to your Gift
                registry, you can add other items by adding this to queue or
                proceed by clicking the continue button
              </p>
            </div>
            <div className="flex justify-center lg:justify-start gap-3 text-base">
              <button
                onClick={onAddMore}
                className=" bg-primary hover:bg-primary/50 text-white font-medium py-3 px-4 md:px-[29px] rounded-full transition-colors">
                Add to queue
              </button>
              <button
                onClick={onContinueAnyway}
                className=" bg-primary-250 hover:bg-primary-250/50 text-primary-650 font-medium py-3 px-4 md:px-[46px] rounded-full transition-colors">
                Continue
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
