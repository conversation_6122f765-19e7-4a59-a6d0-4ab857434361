import { useState, useEffect } from 'react';
import { X } from 'lucide-react';

export const CreateWalletModal = () => {
  const [isOpen, setIsOpen] = useState(true);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (isOpen) {
      const interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval);
            return 100;
          }
          return prev + 2;
        });
      }, 100);

      return () => clearInterval(interval);
    }
  }, [isOpen]);

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleReset = () => {
    setProgress(0);
    setIsOpen(true);
  };

  if (!isOpen) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-100 to-slate-200 flex items-center justify-center p-4">
        <div className="bg-white rounded-xl shadow-lg p-8 text-center">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            Wallet Created Successfully!
          </h2>
          <button
            onClick={handleReset}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors">
            Create Another Wallet
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 to-slate-200 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/20 backdrop-blur-sm"
        onClick={handleClose}
      />

      {/* Modal */}
      <div className="relative bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md mx-4 z-10">
        {/* Close Button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors">
          <X size={20} className="text-gray-500" />
        </button>

        {/* Content */}
        <div className="text-center pt-4">
          {/* Loading Animation */}
          <div className="mb-8">
            <div className="relative w-16 h-16 mx-auto mb-6">
              {/* Outer Ring */}
              <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>

              {/* Animated Ring */}
              <div
                className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-500 animate-spin"
                style={{
                  animation: 'spin 1s linear infinite',
                }}></div>

              {/* Progress Ring */}
              <svg
                className="absolute inset-0 w-full h-full -rotate-90"
                viewBox="0 0 64 64">
                <circle
                  cx="32"
                  cy="32"
                  r="28"
                  fill="none"
                  stroke="#e5e7eb"
                  strokeWidth="4"
                />
                <circle
                  cx="32"
                  cy="32"
                  r="28"
                  fill="none"
                  stroke="#3b82f6"
                  strokeWidth="4"
                  strokeDasharray={`${2 * Math.PI * 28}`}
                  strokeDashoffset={`${
                    2 * Math.PI * 28 * (1 - progress / 100)
                  }`}
                  className="transition-all duration-300 ease-out"
                  strokeLinecap="round"
                />
              </svg>
            </div>
          </div>

          {/* Title */}
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            Creating your Wallet
          </h2>

          {/* Progress Text */}
          <p className="text-sm text-gray-500 mb-4">
            {progress < 100 ? 'Please wait...' : 'Almost ready!'}
          </p>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>

          {/* Progress Percentage */}
          <p className="text-xs text-gray-400">
            {Math.round(progress)}% complete
          </p>
        </div>
      </div>
    </div>
  );
};
